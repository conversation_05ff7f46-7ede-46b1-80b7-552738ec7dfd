const int ROWS = 4;
const int COLS = 5;

int onTime, offTime, duration, divider, ratioOnOff, isDrawing, startDrawing;


// --------------------------------- Matrix ---------------------------------
int M[ROWS][COLS] = {
  { 15, 2, 0, 4, 16 },    // { GPIO15, GPIO2, GPIO0, GPIO4, GPIO16 },
  { 17, 5, 18, 19, 21 },  // { GPIO17, GPIO5, GPIO18, GPIO19, GPIO21 },
  { 22, 23, 13, 32, 33 }, // { GPIO22, GPIO23, GPIO13, GPIO32, GPIO33 },
  { 25, 26, 27, 14, 12 }  // { GPIO25, GPIO26, GPIO27, GPIO14, GPIO12 }
};


// --------------------------------- Shapes ---------------------------------
const int shapeRectangle[ROWS][COLS] = {
  { 1, 1, 1, 1, 1 },
  { 1, 0, 0, 0, 1 },
  { 1, 0, 0, 0, 1 },
  { 1, 1, 1, 1, 1 }
};
const int shapeCircle[ROWS][COLS] = {
  { 0, 1, 1, 1, 0 },
  { 1, 1, 1, 1, 1 },
  { 1, 1, 1, 1, 1 },
  { 0, 1, 1, 1, 0 }
};
const int shapeOpenCircle[ROWS][COLS] = {
  { 0, 1, 1, 1, 0 },
  { 1, 0, 0, 0, 1 },
  { 1, 0, 0, 0, 1 },
  { 0, 1, 1, 1, 0 }
};
const int shapeTriangle[ROWS][COLS] = {
  { 0, 0, 1, 0, 0 },
  { 0, 1, 1, 1, 0 },
  { 1, 1, 1, 1, 1 },
  { 0, 0, 0, 0, 0 }
};
const int shapeVLine[ROWS][COLS] = {
  { 0, 0, 0, 0, 0 },
  { 1, 1, 1, 1, 1 },
  { 0, 0, 0, 0, 0 },
  { 0, 0, 0, 0, 0 }
};
const int shapeHLine[ROWS][COLS] = {
  { 0, 0, 1, 0, 0 },
  { 0, 0, 1, 0, 0 },
  { 0, 0, 1, 0, 0 },
  { 0, 0, 1, 0, 0 }
};
const int shapeDiagonal[ROWS][COLS] = {
  { 1, 0, 0, 0, 0 },
  { 0, 1, 0, 0, 0 },
  { 0, 0, 1, 0, 0 },
  { 0, 0, 0, 1, 0 }
};
const int shapeCross[ROWS][COLS] = {
  { 1, 0, 0, 1, 0 },
  { 0, 1, 1, 0, 0 },
  { 0, 1, 1, 0, 0 },
  { 1, 0, 0, 1, 0 }
};
const int shapeU[ROWS][COLS] = {
  { 1, 0, 0, 0, 1 },
  { 1, 0, 0, 0, 1 },
  { 1, 0, 0, 0, 1 },
  { 0, 1, 1, 1, 0 }
};
const int shapeCup[ROWS][COLS] = {
  { 1, 1, 1, 1, 0 },
  { 1, 1, 1, 1, 1 },
  { 1, 1, 1, 1, 1 },
  { 1, 1, 1, 1, 0 }
};


// --------------------------------- Display it ---------------------------------
void drawShape(const int shape[ROWS][COLS]) {
  for (int row = 0; row < ROWS; row++) {
    for (int col = 0; col < COLS; col++) {
      digitalWrite(M[row][col], shape[row][col] == 1 ? HIGH : LOW);
    }
  }
}

// --------------------------------- Clear it ---------------------------------
void clearM() {
  for (int row = 0; row < ROWS; row++) {
    for (int col = 0; col < COLS; col++) {
      digitalWrite(M[row][col], LOW);
    }
  }
}




void setup() {
  Serial.begin(115200); // Initialize Serial for debugging (IF you are not using GPIO1/3)
  delay(1000); // Give serial a moment to connect

  Serial.println("Setting up GPIO pins as OUTPUT...");

  for (int r = 0; r < ROWS; r++) {
    for (int c = 0; c < COLS; c++) {
      int currentPin = M[r][c];
      // INFO: Pin GPIO%d is a strapping pin. Ensure it's not held LOW at boot by external circuitry if you don't want bootloader mode.
      pinMode(currentPin, OUTPUT);
      digitalWrite(currentPin, LOW); // Optional: set an initial state (e.g., LOW)
    }
  }

  Serial.println("GPIO setup complete.");
}

void loop() {
  // ------------------------------------------------- Inputs -------------------------------------------------
  duration = 8000; // Input from slider or box
  ratioOnOff = 0.8; // Input from slider or box
  divider = 5; // Input from slider or box
  // ---------------------------------------------- Calculations ----------------------------------------------
  duration = duration / divider;
  onTime = duration * ratioOnOff;
  offTime = duration - onTime;
  // ------------------------------------------- Inputs and control -------------------------------------------
  const int (*choosenShape)[COLS] = &shapeRectangle; // Input from dropdown(or from boxes)
  startDrawing = 1; // Input from button
  // ------------------------------------------------- Drawing ------------------------------------------------
  if (startDrawing == 1) {
    isDrawing = 1; // Indicate that it is drawing (not input)
    for (int i = 0; i < divider; i++) {
      drawShape(choosenShape);
      delay(onTime);
      clearM();
      delay(offTime);
    }
    isDrawing = 0;
    startDrawing = 0;
  }
}
